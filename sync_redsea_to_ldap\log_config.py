import logging
from logging.handlers import TimedRotatingFileHandler

# 全局logger实例
_logger = None

def setup_logger() -> logging.Logger:
    global _logger
    if _logger is None:
        # 创建一个logger
        _logger = logging.getLogger()
        _logger.setLevel(logging.DEBUG)

        # 创建控制台处理器，并添加到logger
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 创建文件处理器，并添加到logger
        file_handler = TimedRotatingFileHandler(
            "log/log_redsealdap.log", when="midnight", interval=1, backupCount=5,encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)

        # 创建日志格式器，并设置到处理器
        formatter = logging.Formatter(
            "%(asctime)s - %(filename)s - %(lineno)d - %(levelname)s - %(message)s"
        )
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)

        # 将处理器添加到logger
        _logger.addHandler(console_handler)
        _logger.addHandler(file_handler)

    return _logger