# -*- coding: utf-8 -*-
import requests

from logging.handlers import TimedRotatingFileHandler
from log_config import setup_logger
# 设置全局logger
logger = setup_logger()


class XhesmApi(object):
   
    def __init__(self):
        self.syncDeptUrl = "http://192.168.50.22/xace-web/api/sync/redsea/syncDept"
        self.syncStafftUrl = "http://192.168.50.22/xace-web/api/sync/redsea/syncStaff" 
     #   self.syncDeptUrl = "http://127.0.0.1:30001/api/sync/redsea/syncDept"
     #   self.syncStafftUrl = "http://127.0.0.1:30001/api/sync/redsea/syncStaff"
        self.active = True


    def syncDept(self,depts):
        """
        同步到软件部事务管理系统的部门数据，只取
        :return: 返回一个包含所有部门的列表
        """
        if(self.active == False):
           logger.info("XHESM 用户同步未启用。")
           return
        

        xhesmDept =[]
        for dept in depts.values():
            #判断是否属于软件研发部
            if not dept["orgFullPath"].startswith("深圳市星火电子工程公司-软件研发部"):
                continue
                

            xdept = {"orgId":dept["orgId"],"orgCode":dept["orgCode"],"orgName":dept["orgName"],"parentOrgId":dept["parentOrgId"],"inUse":dept["inUse"]}
            # print(xdept)
            xhesmDept.append(xdept)
        response = requests.post(self.syncDeptUrl, json=xhesmDept) 
        if response.status_code == 200:
            data = response.json()
            code = data.get("code")
            msg = data.get("msg")
            result = data.get("result")

            if code == 200:
                logger.info(f"软件部事务管理系统组织信息同步成功。"+msg) 
            else:
                logger.error(f"软件部事务管理系统组织信息同步失败"+msg)
        else:
            logger.error("软件部事务管理系统组织信息同步失败失败，请检查网络或接口地址")

    def syncUser(self,users):
        """
        同步到软件部事务管理系统的部门数据，只取
        :return: 返回一个包含所有部门的列表
        """
        if(self.active ==False):
           logger.info("XHESM 同步未启用。")
           return
        
        xhesmUsers =[]
        i = 1
        for user in users.values():
            #判断是否属于 罗总 或者是软件部的同事    
            #if(user["externalUser"]=='luofeng' or user['orgCode'].startswith('04')):
            if(user['orgCode'].startswith('04')):
                xuser = {"baseId":user["baseId"],"baseCode":user["baseCode"],"externalUser":user["externalUser"],"staffName":user["staffName"],
                        "sex":user["sex"],"orgId":user["orgId"],"postId":user["postId"],"postNo":user["postNo"],"postName":user["postName"],"postLevelName":user["postLevelName"],
                        "personStatus":user["personStatus"],"mobilePhone":user["mobilePhone"],"personEmail":user.get("personEmail",""),"pictureFile":user.get("pictureFile","")}
                # print(str(i) + ":" +user.get("orgName", "") + ":" + user["staffName"]+":" + user["externalUser"])
                # print(user["staffName"]+":" + user["externalUser"]+":" + user["baseId"])
                i = i+1
                xhesmUsers.append(xuser)
        response = requests.post(self.syncStafftUrl, json=xhesmUsers) 
        if response.status_code == 200:
            data = response.json()
            code = data.get("code")
            msg = data.get("msg")
            result = data.get("result")

            if code == 200:
                logger.info(f"软件部事务管理系统员工同步成功。"+msg) 
            else:
                logger.error(f"软件部事务管理系统员工同步失败"+msg)
        else:
            logger.error("软件部事务管理系统员工失败失败，请检查网络或接口地址")


   