# -*- coding: utf-8 -*-
import requests
import json
import os
import time
from datetime import datetime

from ldap3 import MODIFY_ADD

from redsea import RedseaApi
from xhesm import XhesmApi
from xhim import XhimApi
from ldap import LdapApi
from log_config import setup_logger
import configparser

# 设置全局logger
logger = setup_logger()

class Updater(object):
    def __init__(self, syscode, secret, host, user, password, baseDn, blockedOu, use_ssl):
        """
        :param appkey: 红海云的syscode
        :param secret: 红海云的secret
        :param host: ldap的host
        :param user: ldap的管理员dn
        :param password: ldap的管理员password
        :param baseDn: search base
        :param blockedOu: 失效用户移入的OU
        """
        self.baseDn = baseDn
        self.redsea = RedseaApi(syscode=syscode, secret=secret)
        self.ldap = LdapApi(host=host, user=user, password=password, baseDn=baseDn, use_ssl=use_ssl)
        self.blockedOu = blockedOu
        self.xhesm = XhesmApi()
        self.xhim = XhimApi()
        self.WEBHOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=da6373b0-abd2-43c9-abde-612ddc49b8c5'


    def update_ou(self):
        """
        将红海云的部门更新到LDAP的ou  不存在的ou 目前没处理。
        :return:
        """
        # 0 添加blocked组，用来存放已离职的人
        self.ldap.create_ou(self.blockedOu,None)
        self.ldap.create_ou(self.baseDn,None)

        # 1 获取红海云部门列表，并转化成LDAP可识别的OU列表
        redsea_ou_list = []
        redseaInfo_ou_list = {}

        dept_dict = self.redsea.dept_dict

        #同步到软件部事务管理系统
        self.xhesm.syncDept(dept_dict)
        self.xhim.syncDept(dept_dict)

        for x in dept_dict.values():
            #过滤掉 无效的部门
            if x.get("inUse") != "1":
                continue


            if x['ou']:
                l_dn = x['ou'] + self.baseDn
                redsea_ou_list.append(l_dn)
                redseaInfo_ou_list[l_dn] = x
        redsea_ou_list = sorted(redsea_ou_list, key=lambda i: len(i))
        #print("Redsea ou list: ", redsea_ou_list)

        # 2 获取现在LDAP中的OU列表
        ldap_ou_list = self.ldap.list_ou()
        #print("Ldap ou list: ", ldap_ou_list)

        # 3 轮询红海云ou列表，如果发现在ldap中不存在，则创建之
        add_ou_list = []
        for redsea_ou in redsea_ou_list:
            if redsea_ou not in ldap_ou_list:
                redseaInfo = redseaInfo_ou_list[redsea_ou]
                ou_ldap_attr={"description":redseaInfo['orgId'],"businessCategory":redseaInfo['orgCode'],}
                self.ldap.create_ou(redsea_ou,ou_ldap_attr)
                add_ou_list.append(redsea_ou)
        logger.info("Add ou: {}".format(add_ou_list))

    def update_user(self):
        """
        将红海云的用户更新到LDAP中，以红海云的baseId为唯一标识，Ldap中使用sn存放红海云baseId
        :return:
        """

        logger.info("Redsea dept dict: %d", len(self.redsea.dept_dict))

        # 1 获取红海云用户字典，并附上ldap信息(key为红海云userid，value为红海云中的user对象)
        redsea_user_dict = self.redsea.get_all_user_dict()
        redsea_validuser_dict = {}
        logger.info("Redsea user dict:%d ", len(redsea_user_dict) )

        #同步到软件部事务管理系统
        self.xhesm.syncUser(redsea_user_dict)
        self.xhim.syncUser(redsea_user_dict)

        # 2 获取ldap用户字典(key为用户的sn，value为ldap中的user对象)
        ldap_user_dict = self.ldap.get_all_user_dict()
        logger.info("Ldap user dict: %d", len(ldap_user_dict))        	

        # 3 遍历每一个红海云用户
        for redsea_userId, redsea_user in redsea_user_dict.items():
            #过滤掉无效的数据，就剩下有效的数据进行判断
            if redsea_user.get("personStatus") != "1":
                continue
            
            redsea_validuser_dict[redsea_userId] = redsea_user

            top_dept_id = redsea_user['orgId']
            redsea_ou = self.redsea.dept_dict[top_dept_id]['ou'] + self.ldap.baseDn
            if(redsea_user['personEmail']==None):
                    redsea_user['personEmail'] = ''  
            # 3 判断如果红海云用户不存在LDAP中，则创建新用户
            if redsea_userId not in ldap_user_dict.keys():
                logger.info("创建用户: {}".format(redsea_user['staffName']))
                self.send_wechat_notify("新增用户: {},所在部门：{}/{}".format(redsea_user['staffName'],redsea_user['deptOrgOne'],redsea_user['deptOrgTwo']))

                redsea_name = redsea_user['staffName']
                redsea_mobile = redsea_user['mobilePhone'] if ('mobilePhone' in redsea_user and redsea_user['mobilePhone']) else 0
                redsea_email = redsea_user['personEmail'] if 'personEmail' in redsea_user else ' '
                redsea_title = redsea_user['postName'] if 'postName' in redsea_user else ' '
                redsea_avatar = 'no photo'
                redsea_uid = redsea_user['externalUser']
                base_code = redsea_user['baseCode'].replace('H', '')
                org_code = redsea_user['orgCode']
                self.ldap.create_user(redsea_name, redsea_ou, redsea_userId, redsea_mobile, redsea_email, redsea_title,redsea_uid, redsea_avatar,base_code,org_code)

            # 4 如果红海云用户在LDAP中，对比ou，如果不一致则移动
            else:
                ldap_user = ldap_user_dict[redsea_userId]
                ldap_dn = ldap_user['dn']
                ldap_objectClasses = ldap_user['attributes'].get('objectClass', [])
                # 检查是否需要添加posixAccount
                # logger.info(f"dn: {ldap_dn}.")


                if 'posixAccount' not in ldap_objectClasses:
                    ldap_uid = ldap_user['uid']
                    # if redsea_user['deptOrgTwo'] == "软件二部":
                    logger.info(f"objectclass: {ldap_objectClasses}.")

                    logger.info(f"LDAP user {ldap_uid} at {ldap_dn} does not have 'posixAccount' objectClass. Adding it now.")
                    try:
                        # 尝试添加posixAccount到objectClass
                        # 修改内容：添加posixAccount objectClass
                        base_code = redsea_user['baseCode'].replace('H', '')
                        org_code = redsea_user['orgCode']
                        changes = {
                            'objectClass': [('MODIFY_ADD', ['posixAccount'])],
                            'uidNumber': [('MODIFY_REPLACE', [base_code])],
                            'gidNumber': [('MODIFY_REPLACE', [org_code])],
                            'homeDirectory': [('MODIFY_REPLACE', ["/home/"+ldap_uid])],
                
                        }



                        result = self.ldap.modify_dn(ldap_dn, changes)
                        logger.info(f"Successfully added 'posixAccount' objectClass to {ldap_dn}."+ result)
                    except Exception as e:
                        # 记录任何异常，以便于调试
                        logger.error(f"Failed to add 'posixAccount' objectClass to {ldap_dn}: {e}")

                # 检查用户属性是否需要更新
                need_update = False  
                ldap_cn = ldap_user['attributes']['cn'][0]
                ldap_mobile = ldap_user['attributes']['mobile'][0] if ('mobile' in ldap_user['attributes'] and ldap_user['attributes']['mobile'][0]) else ''
                ldap_mail = ''
                if 'mail' in ldap_user['attributes']:
                    ldap_mail = ldap_user['attributes']['mail'][0] if ldap_user['attributes']['mail'] else ''        
                     

                ldap_title = ldap_user['attributes']['title'][0] if ('title' in ldap_user['attributes'] and ldap_user['attributes']['title'][0]) else ''

                update_fields = {}
                if redsea_user['staffName'] != ldap_cn :
                    update_fields['cn'] = [('MODIFY_REPLACE', [redsea_user['staffName']])]
                if redsea_user['mobilePhone'] != ldap_mobile:
                    update_fields['mobile'] = [('MODIFY_REPLACE', [redsea_user['mobilePhone']])]
                if redsea_user['personEmail'] != ldap_mail :
                    update_fields['mail'] = [('MODIFY_REPLACE', [redsea_user['personEmail'] or ''])]
                if redsea_user['postName'] != ldap_title:
                    update_fields['title'] = [('MODIFY_REPLACE', [redsea_user['postName'] or ''])]

                if update_fields:
                    logger.info("更新用户: {}，更新内容：{}".format(redsea_user['staffName'],update_fields))
                    self.ldap.modify_dn(ldap_dn, update_fields)
                    self.send_wechat_notify("更新用户: {}，更新信息：{}".format(redsea_user['staffName'],update_fields))

                
                ldap_rdn, ldap_ou = ldap_dn.split(',', 1)
                if redsea_ou != ldap_ou:
                    logger.info("移动用户: {}".format(redsea_user['staffName']))
                    self.send_wechat_notify("移动用户: {}，原部门：{}, 新部门：{}".format(redsea_user['staffName'],ldap_ou,redsea_ou))
                    self.ldap.mv_dn(ldap_dn, ldap_rdn, redsea_ou)
        # 打印 redsea_user_dict 的大小
        logger.info("Redsea user dict: %d", len(redsea_user_dict))

        # 5 遍历每一个LDAP用户，如果不再红海云中，则删除密码并移入blockedOu
        for ldap_sn, ldap_user in ldap_user_dict.items():
            if ldap_user['uid'] in IGNORE_UID:
                logger.info("- " + ldap_user['uid'] + " 在IGNORE_UID中，无需移动到Blocked组")
                continue   # 检测是否属于要忽略的UID
            
            # 检查用户当前是否已经在blockedOU中
            ldap_dn = ldap_user['dn']
            ldap_rdn, current_ou = ldap_dn.split(',', 1)
            
            # 判断用户状态：
            # 1. 不在红海云中
            # 2. 在红海云中但已离职 (personStatus != "1")
            # 3. 不在blockedOU中
            should_block = False
            reason = ""
            
            if ldap_sn not in redsea_user_dict.keys():
                should_block = True
                reason = "用户不在红海云系统中"
            elif redsea_user_dict[ldap_sn].get("personStatus") != "1":
                should_block = True
                reason = "用户在红海云系统中已离职"
            
            if should_block and current_ou != self.blockedOu:
                logger.info(f"- 冻结用户: {ldap_dn}，原因：{reason}")
                try:
                    # 使用LDAP中的信息
                    user_name = ldap_user['attributes']['cn'][0] if 'cn' in ldap_user['attributes'] else ldap_user['uid']
                    self.send_wechat_notify(f"禁用用户: {user_name}, 从 {current_ou} 移动到 {self.blockedOu}，原因：{reason}")
                except Exception as e:
                    logger.error(f"发送企业微信通知失败: {e}")
                
                # 确保这些操作能执行，不受通知发送失败影响
                try:
                    # 先删除用户密码
                    self.ldap.delete_password(ldap_dn)
                    logger.info(f"用户 {ldap_user['uid']} 密码已清除")
                    
                    # 移动用户到blockedOU
                    result = self.ldap.mv_dn(ldap_dn, ldap_rdn, self.blockedOu)
                    logger.info(f"用户 {ldap_user['uid']} 移动结果: {result}")
                except Exception as e:
                    logger.error(f"移动用户到blockedOU失败: {e}")

    def send_wechat_notify(self, message):
        headers = {'Content-Type': 'application/json'}
        data = {
            "msgtype": "text",
            "text": {
                "content": message
            }
        }
        response = requests.post(self.WEBHOOK_URL, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            logger.info("企业微信发送成功")
        else:
            logger.info(f"企业微信发送失败. Status Code: {response.status_code}")


IGNORE_UID =[]

def timedTask():
    # 读取config.ini文件
    config = configparser.ConfigParser()
    config.read('config.ini',encoding='utf-8')

    syscode = config['REDSEA']['REDSEA_SYSCODE']
    secret = config['REDSEA']['REDSEA_SECRET']
    host = config['LDAP']['LDAP_HOST']
    user = config['LDAP']['LDAP_USER']
    passwd = config['LDAP']['LDAP_PASSWD']
    search_base = config['LDAP']['LDAP_SEARCH_BASE']
    blocked_ou = config['LDAP']['LDAP_BLOCKED_OU']
    use_ssl = config['LDAP']['LDAP_USE_SSL']
    sync_interval = int(config['OPTION']['SYNC_INTERVAL'])
    IGNORE_UID  = str(config['OPTION']['IGNORE_UID']).split(",") 
    
    """
    定时执行任务
    :return:
    """
    # while True:
    logger.info("==》 同步任务开始")
    updater = Updater(syscode, secret, host, user, passwd, search_base, blocked_ou, use_ssl)
    updater.update_ou()
    updater.update_user()
    logger.info("==》 同步任务结束")
    logger.info("")

        # time.sleep(sync_interval)


if __name__ == '__main__':
    timedTask()
