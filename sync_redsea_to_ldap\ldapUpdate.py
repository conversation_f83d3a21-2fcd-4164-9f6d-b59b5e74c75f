
from ldap import LdapApi
from log_config import setup_logger
import configparser

# 设置全局logger
logger = setup_logger()

 # 读取config.ini文件
config = configparser.ConfigParser()
config.read('config.ini',encoding='utf-8')

host = config['LDAP']['LDAP_HOST']
user = config['LDAP']['LDAP_USER']
passwd = config['LDAP']['LDAP_PASSWD']
search_base = config['LDAP']['LDAP_SEARCH_BASE']
blocked_ou = config['LDAP']['LDAP_BLOCKED_OU']

ldap = LdapApi(host=host, user=user, password=passwd, baseDn=search_base, use_ssl=True)


# 2 获取ldap用户字典(key为用户的sn，value为ldap中的user对象)
ldap_user_dict = ldap.get_all_user_dict()
logger.info("Ldap user dict: %d", len(ldap_user_dict))  
# 5 遍历每一个LDAP用户，如果不再红海云中，则删除密码并移入blockedOu
for ldap_sn, ldap_user in ldap_user_dict.items():
    #print(ldap_user)
    # print(ldap_user['uid'])
    # print(cfg.IGNORE_UID)
    ldap_dn = ldap_user['dn']
    ldap_rdn, _ = ldap_dn.split(',', 1)
    cn = ldap_user['attributes']["cn"][0]
    uid = ldap_user['attributes']["uid"][0]
    cc = cn+"-"+uid
    print(cc)

    update_fields = {}
    update_fields['displayName'] = [('MODIFY_REPLACE', cc)]
    ldap.modify_dn(ldap_dn, update_fields)
