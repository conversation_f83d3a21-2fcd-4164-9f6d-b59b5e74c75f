# -*- coding: utf-8 -*-
import json
import requests

from logging.handlers import TimedRotatingFileHandler
from log_config import setup_logger
# 设置全局logger
logger = setup_logger()


class XhimApi(object):
   
    def __init__(self):
        print("..INIT XhesmApi.. ")
        self.syncDeptUrl = "http://192.168.50.50:59000/xace/api/sync/redsea/syncDept"
        self.syncStafftUrl = "http://192.168.50.50:59000/xace/api/sync/redsea/syncStaff"
        # self.syncDeptUrl = "http://127.0.0.1:30001/api/sync/redsea/syncDept"
        # self.syncStafftUrl = "http://127.0.0.1:30001/api/sync/redsea/syncStaff"
        self.active = True



    def syncDept(self,depts):
        """
        同步到XHIM的部门数据，只取
        :return: 返回一个包含所有部门的列表
        """

        if(self.active ==False):
           logger.info("XHIM 同步未启用。")
           return
        
        xhesmDept =[]
        for dept in depts.values():  
            if  dept["orgCode"]=='xhdz':
                continue          

            xdept = {"orgId":dept["orgId"],"orgCode":dept["orgCode"],"orgName":dept["orgName"],"parentOrgId":dept["parentOrgId"],"inUse":dept["inUse"],"struOrder":dept["struOrder"]}
            # print(xdept)
            xhesmDept.append(xdept)

        # 保存部门数据到本地文件
        try:
            with open("xhesm_dept.json", 'w', encoding='utf-8') as file:
                json.dump(xhesmDept, file, ensure_ascii=False, indent=4)
            logger.info(f"部门数据已保存到 xhesm_dept.json，共 {len(xhesmDept)} 个部门")
        except Exception as e:
            logger.error(f"保存部门数据到文件失败: {str(e)}")

        response = requests.post(self.syncDeptUrl, json=xhesmDept)
        if response.status_code == 200:
            data = response.json()
            code = data.get("code")
            msg = data.get("msg")
            result = data.get("result")

            if code == 200:
                logger.info(f"XHIM系统组织信息同步成功。"+msg) 
            else:
                logger.error(f"XHIM组织信息同步失败"+msg)
        else:
            logger.error("XHIM组织信息同步失败失败，请检查网络或接口地址")

    def syncUser(self,users):
        """
        同步XHIM部门数据
        :return: 返回一个包含所有部门的列表
        """
        if(self.active ==False):
           logger.info("XHIM 同步未启用。")
           return
        
        xhesmUsers =[]
        i = 1
        for user in users.values(): 
            xuser = {"baseId":user["baseId"],"baseCode":user["baseCode"],"externalUser":user["externalUser"],"staffName":user["staffName"],
                        "sex":user["sex"],"orgId":user["orgId"],"postId":user["postId"],"postNo":user["postNo"],"postName":user["postName"],"postLevelName":user["postLevelName"],
                        "personStatus":user["personStatus"],"mobilePhone":user["mobilePhone"],"personEmail":user.get("personEmail",""),"pictureFile":user.get("pictureFile","")}
            # print(str(i) + ":" +user.get("orgName", "") + ":" + user["staffName"]+":" + user["externalUser"])
            # print(user["staffName"]+":" + user["externalUser"]+":" + user["baseId"])
            i = i+1
            xhesmUsers.append(xuser)

        # 保存用户数据到本地文件
        try:
            with open("xhesm_users.json", 'w', encoding='utf-8') as file:
                json.dump(xhesmUsers, file, ensure_ascii=False, indent=4)
            logger.info(f"用户数据已保存到 xhesm_users.json，共 {len(xhesmUsers)} 个用户")
        except Exception as e:
            logger.error(f"保存用户数据到文件失败: {str(e)}")

        response = requests.post(self.syncStafftUrl, json=xhesmUsers) 
        # print(xhesmUsers)
        
        if response.status_code == 200:
            data = response.json()
            code = data.get("code")
            msg = data.get("msg")
            result = data.get("result")

            if code == 200:
                logger.info(f"系统员工同步成功。"+msg) 
            else:
                logger.error(f"系统员工同步失败"+msg)
        else:
            logger.error("系统员工失败失败，请检查网络或接口地址")


   