2025-02-28 12:44:46,556 - main.py - 288 - INFO - ==》 同步任务开始
2025-02-28 12:44:46,559 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:44:46,930 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/customerapi/common/openApi/gettoken.mob?syscode=tdoa&timestamp=1740717886558&sign=62c3ad90d08ee2e2abe19bbecb97f763 HTTP/1.1" 200 None
2025-02-28 12:44:46,932 - redsea.py - 44 - INFO - 认证信息获取成功.token:933f71803c16cf53b40b74cf030a4904
2025-02-28 12:44:46,934 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:44:47,565 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/authorization/api/v2/queryAllOrgInfo.mob?pageSize=200&pageNo=1&orgCode= HTTP/1.1" 200 None
2025-02-28 12:44:48,277 - ldap.py - 132 - INFO - Add ou=Blocked,dc=xinghuo,dc=com result: False
2025-02-28 12:44:48,363 - ldap.py - 132 - INFO - Add ou=People,dc=xinghuo,dc=com result: False
2025-02-28 12:44:48,365 - connectionpool.py - 241 - DEBUG - Starting new HTTP connection (1): 192.168.50.22:80
2025-02-28 12:44:48,649 - connectionpool.py - 544 - DEBUG - http://192.168.50.22:80 "POST /xace-web/api/sync/redsea/syncDept HTTP/1.1" 200 None
2025-02-28 12:44:48,650 - xhesm.py - 48 - INFO - 软件部事务管理系统组织信息同步成功。同步完成。新增部门: 0个, 修改部门: 0个, 忽略部门: 52个
2025-02-28 12:44:48,651 - connectionpool.py - 241 - DEBUG - Starting new HTTP connection (1): 192.168.50.50:59000
2025-02-28 12:44:49,493 - connectionpool.py - 544 - DEBUG - http://192.168.50.50:59000 "POST /xace/api/sync/redsea/syncDept HTTP/1.1" 200 None
2025-02-28 12:44:49,493 - xhim.py - 49 - INFO - XHIM系统组织信息同步成功。同步完成。新增部门: 0个, 修改部门: 5个, 忽略部门: 157个
2025-02-28 12:44:49,663 - main.py - 84 - INFO - Add ou: []
2025-02-28 12:44:49,664 - main.py - 92 - INFO - Redsea dept dict: 163
2025-02-28 12:44:49,665 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:44:52,784 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/authorization/api/v2/queryAllStaffBaseInfo.mob?pageNo=1&pageSize=500&orgCode= HTTP/1.1" 200 None
2025-02-28 12:44:53,202 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:44:56,121 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/authorization/api/v2/queryAllStaffBaseInfo.mob?pageNo=2&pageSize=500&orgCode= HTTP/1.1" 200 None
2025-02-28 12:44:56,475 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:44:59,195 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/authorization/api/v2/queryAllStaffBaseInfo.mob?pageNo=3&pageSize=500&orgCode= HTTP/1.1" 200 None
2025-02-28 12:44:59,419 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:45:01,774 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/authorization/api/v2/queryAllStaffBaseInfo.mob?pageNo=4&pageSize=500&orgCode= HTTP/1.1" 200 None
2025-02-28 12:45:01,775 - main.py - 97 - INFO - Redsea user dict:1273 
2025-02-28 12:45:01,778 - connectionpool.py - 241 - DEBUG - Starting new HTTP connection (1): 192.168.50.22:80
2025-02-28 12:45:02,343 - connectionpool.py - 544 - DEBUG - http://192.168.50.22:80 "POST /xace-web/api/sync/redsea/syncStaff HTTP/1.1" 200 None
2025-02-28 12:45:02,343 - xhesm.py - 86 - ERROR - 软件部事务管理系统员工同步失败系统异常
2025-02-28 12:45:02,365 - connectionpool.py - 241 - DEBUG - Starting new HTTP connection (1): 192.168.50.50:59000
2025-02-28 12:45:04,827 - connectionpool.py - 544 - DEBUG - http://192.168.50.50:59000 "POST /xace/api/sync/redsea/syncStaff HTTP/1.1" 200 None
2025-02-28 12:45:04,828 - xhim.py - 87 - INFO - 系统员工同步成功。红海云同步人员结果：新增：0[],修改：33[司晨飞[U],何俊清[U],张尧尧[U],郝志鹏[U],陈晓松[U],刘新[U],张海明[U],刘浩志[U],高阳[U],李朝森[U],付强[U],雷庆玲[U],唐博[U],张柳媚[U],曾涛[U],孔凡夫[U],李启航[U],钟晶晶[U],李葵阕[U],刘佳[U],吴扬姿[U],肖敏[U],杨万村[U],刘献丽[U],柳婷[U],陈丹丹[U],林威宇[U],刘世林[U],林俊睿[U],许晓彬[U],林恒旭[U],徐丽娜[U],唐雯雯[U],],忽略：1239
2025-02-28 12:45:05,544 - main.py - 105 - INFO - Ldap user dict: 982
2025-02-28 12:45:05,548 - main.py - 203 - INFO - Redsea user dict: 1273
2025-02-28 12:45:05,552 - main.py - 292 - INFO - ==》 同步任务结束
2025-02-28 12:45:05,552 - main.py - 293 - INFO - 
2025-02-28 12:49:28,922 - main.py - 288 - INFO - ==》 同步任务开始
2025-02-28 12:49:28,924 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:49:29,220 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/customerapi/common/openApi/gettoken.mob?syscode=tdoa&timestamp=1740718168923&sign=04ca7f756c6ddd8570523a83aa498780 HTTP/1.1" 200 None
2025-02-28 12:49:29,221 - redsea.py - 44 - INFO - 认证信息获取成功.token:933f71803c16cf53b40b74cf030a4904
2025-02-28 12:49:29,222 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:49:29,833 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/authorization/api/v2/queryAllOrgInfo.mob?pageSize=200&pageNo=1&orgCode= HTTP/1.1" 200 None
2025-02-28 12:49:30,437 - ldap.py - 132 - INFO - Add ou=Blocked,dc=xinghuo,dc=com result: False
2025-02-28 12:49:30,529 - ldap.py - 132 - INFO - Add ou=People,dc=xinghuo,dc=com result: False
2025-02-28 12:49:30,531 - connectionpool.py - 241 - DEBUG - Starting new HTTP connection (1): 192.168.50.22:80
2025-02-28 12:49:30,781 - connectionpool.py - 544 - DEBUG - http://192.168.50.22:80 "POST /xace-web/api/sync/redsea/syncDept HTTP/1.1" 200 None
2025-02-28 12:49:30,782 - xhesm.py - 48 - INFO - 软件部事务管理系统组织信息同步成功。同步完成。新增部门: 0个, 修改部门: 0个, 忽略部门: 52个
2025-02-28 12:49:30,785 - connectionpool.py - 241 - DEBUG - Starting new HTTP connection (1): 192.168.50.50:59000
2025-02-28 12:49:31,600 - connectionpool.py - 544 - DEBUG - http://192.168.50.50:59000 "POST /xace/api/sync/redsea/syncDept HTTP/1.1" 200 None
2025-02-28 12:49:31,600 - xhim.py - 49 - INFO - XHIM系统组织信息同步成功。同步完成。新增部门: 0个, 修改部门: 5个, 忽略部门: 157个
2025-02-28 12:49:31,764 - main.py - 84 - INFO - Add ou: []
2025-02-28 12:49:31,765 - main.py - 92 - INFO - Redsea dept dict: 163
2025-02-28 12:49:31,765 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:49:35,098 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/authorization/api/v2/queryAllStaffBaseInfo.mob?pageNo=1&pageSize=500&orgCode= HTTP/1.1" 200 None
2025-02-28 12:49:35,485 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:49:38,473 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/authorization/api/v2/queryAllStaffBaseInfo.mob?pageNo=2&pageSize=500&orgCode= HTTP/1.1" 200 None
2025-02-28 12:49:38,850 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:49:41,558 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/authorization/api/v2/queryAllStaffBaseInfo.mob?pageNo=3&pageSize=500&orgCode= HTTP/1.1" 200 None
2025-02-28 12:49:41,754 - connectionpool.py - 1049 - DEBUG - Starting new HTTPS connection (1): 192.168.50.210:443
2025-02-28 12:49:43,998 - connectionpool.py - 544 - DEBUG - https://192.168.50.210:443 "GET /RedseaPlatform/third/authorization/api/v2/queryAllStaffBaseInfo.mob?pageNo=4&pageSize=500&orgCode= HTTP/1.1" 200 None
2025-02-28 12:49:44,000 - main.py - 97 - INFO - Redsea user dict:1273 
2025-02-28 12:49:44,004 - connectionpool.py - 241 - DEBUG - Starting new HTTP connection (1): 192.168.50.22:80
2025-02-28 12:49:44,669 - connectionpool.py - 544 - DEBUG - http://192.168.50.22:80 "POST /xace-web/api/sync/redsea/syncStaff HTTP/1.1" 200 None
2025-02-28 12:49:44,670 - xhesm.py - 84 - INFO - 软件部事务管理系统员工同步成功。红海云同步人员结果：新增：3[曾涛,李启航,刘佳,],修改：2[唐博[U],侯展志[禁用],],忽略：306
2025-02-28 12:49:44,693 - connectionpool.py - 241 - DEBUG - Starting new HTTP connection (1): 192.168.50.50:59000
2025-02-28 12:49:47,200 - connectionpool.py - 544 - DEBUG - http://192.168.50.50:59000 "POST /xace/api/sync/redsea/syncStaff HTTP/1.1" 200 None
2025-02-28 12:49:47,201 - xhim.py - 87 - INFO - 系统员工同步成功。红海云同步人员结果：新增：0[],修改：33[司晨飞[U],何俊清[U],张尧尧[U],郝志鹏[U],陈晓松[U],刘新[U],张海明[U],刘浩志[U],高阳[U],李朝森[U],付强[U],雷庆玲[U],唐博[U],张柳媚[U],曾涛[U],孔凡夫[U],李启航[U],钟晶晶[U],李葵阕[U],刘佳[U],吴扬姿[U],肖敏[U],杨万村[U],刘献丽[U],柳婷[U],陈丹丹[U],林威宇[U],刘世林[U],林俊睿[U],许晓彬[U],林恒旭[U],徐丽娜[U],唐雯雯[U],],忽略：1239
2025-02-28 12:49:47,935 - main.py - 105 - INFO - Ldap user dict: 982
2025-02-28 12:49:47,939 - main.py - 203 - INFO - Redsea user dict: 1273
2025-02-28 12:49:47,943 - main.py - 292 - INFO - ==》 同步任务结束
2025-02-28 12:49:47,943 - main.py - 293 - INFO - 
