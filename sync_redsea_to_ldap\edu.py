# -*- coding: utf-8 -*-
import json
import requests
import sys
import time
from logging.handlers import TimedRotatingFileHandler
import hashlib
from log_config import setup_logger 
logger = setup_logger()

class EduApi(object):
    def get_basecodes_from_file(self, file_path):
        with open(file_path, 'r') as file:
            basecodes = [line.strip() for line in file.readlines()]
        return basecodes

    def fetch_support_info(self, file_path):
        basecodes = self.get_basecodes_from_file(file_path)
        all_user_titles = {}
        for basecode in basecodes:
            user_titles = self.get_all_user_title(basecode)
            all_user_titles.update(user_titles)
        return all_user_titles

    def get_all_user_title(self, baseCode):
            user_dict = {}
            base_psn_url = "https://ehr.szxhdz.com/RedseaPlatform/third/authorization/api/v2/queryStaffTitleInfo.mob"
            syscode = "tdoa"
            headers = {"Authorization": '4688672a381c297a2d3b25913d26f847', "Syscode": 'tdoa'}
            # 初始页码和每页的记录数

           
                # 定义请求参数
            params = {"baseCode": baseCode}
            response = requests.get(base_psn_url, headers=headers, params=params)
            if response.status_code == 200:
                data = response.json()
                code = data.get("code")
                if code == "200":
                    datas = data.get("result")

                    if not datas:
                        # 所有数据已经获取完毕，退出循环
                        return user_dict
                    for staff in datas:
                        # if staff.get("personStatus") != "1":
                        #     continue
                        print(baseCode, staff["staffName"], staff["title"],staff["reviewUnit"],staff["uploadFile"])
                        user_dict[staff["baseId"]] = staff         
                


       
            return user_dict


if __name__ == "__main__":
    edu_api = EduApi()
    file_path = "rjb.txt"
    support_info = edu_api.fetch_support_info(file_path)
    print(support_info)