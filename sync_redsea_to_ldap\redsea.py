# -*- coding: utf-8 -*-
import json
import requests
import sys
import time
from logging.handlers import TimedRotatingFileHandler
import hashlib
from log_config import setup_logger
# 设置全局logger
logger = setup_logger()


class RedseaApi(object):
    def __init__(self, syscode,secret):
        """
        :param syscode:  红海云应用的唯一标识key
        :param secret_key: 红海云应用的密钥。
        """
        self.syscode = syscode
        self.secret = secret
        self.token = self.access_token()
        self.dept_dict = self.gen_dept_dict()

    def access_token(self):
        """
        获取红海云应用的access_token
        :return: access_token
        """ 

        # base_auth_url = "https://ehr.szxhdz.com:8088/RedseaPlatform/third/customerapi/common/openApi/gettoken.mob"
        base_auth_url = "https://192.168.50.210/RedseaPlatform/third/customerapi/common/openApi/gettoken.mob"
        timestamp = str(int(time.time() * 1000))  # 当前时间戳（毫秒级）

        sign = hashlib.md5(f"{self.syscode}&{timestamp}&{self.secret}".encode()).hexdigest()
        params = {"syscode": self.syscode, "timestamp": timestamp, "sign": sign}
        response = requests.get(base_auth_url, params=params,verify=False)
        if response.status_code == 200:
            data = response.json()
            code = data.get("code")
            msg = data.get("msg")
            result = data.get("result")

            if code == "200":
                logger.info("认证信息获取成功.token:"+result)
                return result
            else:
                logger.error(f"认证信息获取失败: {msg}")
        else:
            logger.error("认证信息请求失败，请检查网络或接口地址")
            




    def get_all_dept(self):
        """
        获取企业的所有部门的列表
        :return: 返回一个包含所有部门的列表
        """
        base_org_url = "https://192.168.50.210/RedseaPlatform/third/authorization/api/v2/queryAllOrgInfo.mob"
        syscode = "tdoa"
        params = {
            "pageSize": 200,  # 每页记录数，根据需求调整
            "pageNo": 1,  # 页码
            "orgCode": "",  # 组织编号，如果不需要特定组织，可以省略
        }

        headers = {"Authorization": self.token, "Syscode": syscode}
        response = requests.get(base_org_url, params=params, headers=headers,verify=False) 
        depts = []
        if response.status_code == 200:
            data = response.json()
            code = data.get("code")
            msg = data.get("msg")
            result = data.get("result")

            if code == "200":
                datas = result.get("datas")
                for dept in datas:
                    # if dept.get("inUse") != "1":
                    #     continue
                    depts.append(dept)
                return depts   
            else:
                logger.error(f"组织信息请求失败: {msg}")
        else:
            logger.error("组织信息请求失败，请检查网络或接口地址")




    def get_dept_user(self, dept_id):
        """
        :param department_id: 部门ID varchar
        :return: 部门的用户列表 list
        """
        url = 'https://192.168.50.210/RedseaPlatform/third/authorization/api/v2/queryAllStaffBaseInfo.mob'
        headers = {"Authorization": self.token, "Syscode": self.syscode}
        # 定义请求参数
        params = {"pageNo": page, "pageSize": page_size, "orgCode": ""}

        # 初始页码和每页的记录数
        page = 1
        page_size = 500  # 最大不超过500条
        num = 0

        # url = 'https://oapi.dingtalk.com/topapi/v2/user/list?access_token=%s' % self.token
        # data = {'dept_id': dept_id, 'cursor': 0, "size": 100}
        ret = requests.get(url,verify=False)
        res = json.loads(ret.text)
        return res

    def get_dept_id(self, dept_name):
        """
        :param department_name: 部门名称 string
        :return: 部门ID int
        """
        department_list = self.get_all_dept()['department']
        department_id = [department['id'] for department in department_list if department['name'] == dept_name]
        if len(department_id) < 1:
            # raise ValueError("Can`t find department {}".format(department_name))
            print("Can`t find department {}, return 0.".format(dept_name))
            return 0  # 表示无此部门
        else:
            return department_id[0]  # 根据传给的部门名字，返回部门id

    def get_sub_dept(self, department_id):
        """
        :param department_id: 父部门id
        :return: 子部门id list
        """
        url = 'https://oapi.dingtalk.com/department/list_ids?access_token=%s&id=%s' % (self.token, department_id)
        ret = requests.get(url)
        res = json.loads(ret.text)
        return res

    def get_all_users(self):
        user_list = []
        all_dept_id = [dept['id'] for dept in self.get_all_dept()['department']]
        # print(all_dept_id)
        for dept_id in all_dept_id:
            user_list.extend(self.get_dept_user(dept_id)['userlist'])
        return user_list

    def get_all_user_dict(self):
        user_dict = {}
        base_psn_url = "https://192.168.50.210/RedseaPlatform/third/authorization/api/v2/queryAllStaffBaseInfo.mob"
        syscode = "tdoa"
        headers = {"Authorization": self.token, "Syscode": self.syscode}
    
        # 初始页码和每页的记录数
        page = 1
        page_size = 500  # 最大不超过500条
        num = 0
        while True:
            # 定义请求参数
            params = {"pageNo": page, "pageSize": page_size, "orgCode": ""}
            response = requests.get(base_psn_url, headers=headers, params=params,verify=False)
            if response.status_code == 200:
                data = response.json()
                code = data.get("code")
                if code == "200":
                    datas = data.get("result").get("datas")

                    if not datas:
                        # 所有数据已经获取完毕，退出循环
                        break
                    for staff in datas:
                        # if staff.get("personStatus") != "1":
                        #     continue
                        user_dict[staff["baseId"]] = staff
                   
            

                    # 增加页码以获取下一页数据
                    page += 1
                else:
                    logger.error(f"请求失败: {data.get('msg')}")
                    break
            else:
                logger.error("请求失败，请检查网络或接口地址")
                break
        return user_dict


    def gen_dept_dict(self):
        dept_dict = {}
        dept_all = self.get_all_dept()
        # 生成一个部门字典，key为部门id，value为部门属性对象
        for i in dept_all:
            dept_dict[i["orgId"]] = i
        # 将上面生成的部门字典，每个value都添加ou字段前缀
        for dept_id, dept in dept_dict.items():
            ou = ''
            while dept['parentOrgId'] !=None:
                ou = ou + "ou=" + dept['orgName'] + ","
                dept = dept_dict[dept['parentOrgId']]
            #print(ou)
            # 添加ou属性
            dept_dict[dept_id]['ou'] = ou
        return dept_dict