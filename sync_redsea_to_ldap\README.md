### 软件功能
定期同步公司红海云通讯录（组织架构、员工信息），实现入职、离职账号自动创建、锁定等能力

### 部署方式
参考k8s目录下的yaml，调整configmap配置后直接部署(未启用，目前使用的是配置文件 config.ini)

### LDAP结构设计
- 根OU设计：
  1. Blocked ou：离职员工自动移动到此ou，并锁定
  2. Group ou：权限组，用于对接其他应用系统，例如Wiki、Rancher、SVN等内部服务，分别创建group并单独配置组内用户
  3. People ou：定时从红海云同步过来的组织架构和人员信息
![](https://img-1257855627.cos.ap-shanghai.myqcloud.com/2023-10-26-TkMiol.png)

- 用户信息
这里使用ldap用户的sn属性和红海云的userid对应，成为后期判断用户唯一的关键属性。用户密码自动随机生成
  ![](https://img-1257855627.cos.ap-shanghai.myqcloud.com/2023-10-26-7hnitj.png)



## 创建用于读取企业通讯录的红海云应用

> **说明**：LDAP统一认证系统需要从红海云OpenAPI接口读取企业通讯录信息（包含部门、员工信息），周期性探测，从而实现员工入职、离职信息的获取。为了实现此功能，需申请红海云应用.

红海云相关信息：
[REDSEA]
REDSEA_SYSCODE = tdoa
REDSEA_SECRET = dsafdsafew23rfe332few654

API文档参考：
https://zixun.hr-soft.cn/uploadfile//2022/010/19/1666159771736.html?f=121

### 红海云API的问题
1、无法获取非上班日的考勤。
2、模拟登录需要实际使用人每3天登录一次。


参考的开源工程地址：https://gitee.com/foxpast/sync_ding_to_ldap




    