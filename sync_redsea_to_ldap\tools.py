# -*- coding: utf-8 -*-
import re

from pypinyin import lazy_pinyin  # please use python3


def convert_chinese_to_pinyin(chinese):
    value = lazy_pinyin(chinese)
    # print(value)
    res = ''
    for i in value:
        res = res + i
    return res
    # pinyin = str.capitalize(value[0])
    # if len(value) > 1:
    #     pinyin = pinyin + " " + str.capitalize(value[1])
    # if len(value) > 2:
    #     for i in range(2, len(value)):
    #         pinyin = pinyin + value[i]
    # print(pinyin)
    # return pinyin


def name_add_number(name):
    # 将输入名字的末尾加上数字
    num = re.findall('\d+$', name)  # 提取名字末尾的数字
    if num:  # 如果结尾有数字，就让此数字加1
        num = int(num[0])
        new_num = num + 1
        new_num = str(new_num)

        name = name.split(str(num))[0] + new_num
    else:  # 如果结尾没有数字，就在结尾添加数字1
        name = name + "1"
    return name


def get_random_password(lenth):
    import random
    import string
    a = string.ascii_letters + string.digits
    key = random.sample(a, lenth)
    keys = "".join(key)
    return keys


if __name__ == '__main__':
    print(get_random_password(20))
