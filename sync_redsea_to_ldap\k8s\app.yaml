apiVersion: apps/v1
kind: Deployment
metadata:
  name: sync-ding-to-ldap
  namespace: ops
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sync-ding-to-ldap
  template:
    metadata:
      labels:
        app: sync-ding-to-ldap
    spec:
      containers:
      - name: sync-ding-to-ldap
        image: registry.cn-zhangjiakou.aliyuncs.com/tx-k8s-dev/ops:sync-ding-to-ldap-1.4.8
        envFrom:
          - configMapRef:
              name: sync-ding-to-ldap-config
      restartPolicy: Always
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: sync-ding-to-ldap-config
  namespace: ops
data:
  LDAP_HOST: ldap.example.com
  LDAP_USER: cn=admin,dc=example,dc=com
  LDAP_PASSWD: <xxxx>
  LDAP_SEARCH_BASE: ou=People,dc=example,dc=com
  LDAP_BLOCKED_OU: ou=Blocked,dc=example,dc=com
  LDAP_USE_SSL: "False"
  DINGTALK_APPKEY: <xxx>
  DINGTALK_APPSECRET: <xxx>
  SYNC_INTERVAL: "300"
  IGNORE_UID: "test,root,rancher"    # 忽略的用户uid，多个则使用逗号隔开。忽略的用户不会被自动移动到Blocked组中